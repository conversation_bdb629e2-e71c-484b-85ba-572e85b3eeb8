<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import * as PIXI from 'pixi.js';
  import { TilesetManager, WorldBuilder, JUNGLE_TILESET_CONFIG } from '$lib/utils/tileUtils';

  // Props
  export let visible = false;

  // Canvas element reference
  let canvasContainer: HTMLDivElement;
  let pixiApp: PIXI.Application;
  let toolInitialized = false;

  // Screen dimensions
  let screenWidth = 800;
  let screenHeight = 600;
  
  // Tool objects
  let tilesetManager: TilesetManager;
  let worldBuilder: WorldBuilder;
  let worldContainer: PIXI.Container;
  let gridContainer: PIXI.Container;
  let uiContainer: PIXI.Container;

  // Tool state
  let selectedTileId = 0;
  let gridSize = 32; // Default grid size (16px tiles * 2 scale)
  let showGrid = true;
  let showTileBorders = true;
  let placedTiles: Array<{id: number, x: number, y: number, scale: number, rotation?: number, flipX?: boolean, flipY?: boolean, layer?: number}> = [];
  let availableTiles: number[] = [];

  // Jungle platform tile mappings (6x6 grid)
  const jungleTileMap = {
    // Platform structure tiles (outer edges)
    platform: {
      topLeft: 0,     // (0,0)
      topCenter: 1,   // (1,0)
      topRight: 5,    // (5,0)
      leftSide: 16,   // (0,1)
      rightSide: 21,  // (5,1)
      bottomLeft: 80, // (0,5)
      bottomCenter: 81, // (1,5)
      bottomRight: 85 // (5,5)
    },
    // Foliage tiles (inner area)
    foliage: {
      topLeft: 17,    // (1,1)
      topCenter: 18,  // (2,1)
      topRight: 20,   // (4,1)
      centerLeft: 33, // (1,2)
      center: 34,     // (2,2)
      centerRight: 36, // (4,2)
      bottomLeft: 65, // (1,4)
      bottomCenter: 66, // (2,4)
      bottomRight: 68 // (4,4)
    }
  };

  // Preset tile categories for easier selection
  let selectedCategory = 'platform';
  let selectedTileType = 'topLeft';

  // UI state
  let mouseX = 0;
  let mouseY = 0;
  let snappedX = 0;
  let snappedY = 0;
  let hoveredTileIndex = -1;

  // Drag state
  let isDragging = false;
  let draggedTileIndex = -1;
  let dragStartX = 0;
  let dragStartY = 0;
  let dragOffsetX = 0;
  let dragOffsetY = 0;

  // Undo/Redo state
  let undoStack: string[] = [];
  let redoStack: string[] = [];
  const maxUndoSteps = 50; // Limit undo history to prevent memory issues

  // Copy/Paste state
  let copiedTile: {id: number, scale: number} | null = null;

  // Keyboard handler reference for cleanup
  let keydownHandler: ((e: KeyboardEvent) => void) | null = null;

  // Layer management
  let layers = [
    { name: 'Background', container: new PIXI.Container(), visible: true, zIndex: 1 },
    { name: 'Midground', container: new PIXI.Container(), visible: true, zIndex: 2 },
    { name: 'Foreground', container: new PIXI.Container(), visible: true, zIndex: 3 }
  ];
  let activeLayerIndex = 1; // Default to midground

  $: if (visible && browser && !toolInitialized) {
    initializeTool();
  }

  $: if (!visible && toolInitialized) {
    cleanup();
  }

  onDestroy(() => {
    cleanup();
  });

  function updateScreenDimensions() {
    screenWidth = window.innerWidth;
    screenHeight = window.innerHeight - 120; // Account for header and tool UI
  }

  async function initializeTool() {
    if (toolInitialized) return;

    try {
      updateScreenDimensions();

      // Create PixiJS application
      pixiApp = new PIXI.Application();
      await pixiApp.init({
        width: screenWidth,
        height: screenHeight,
        backgroundColor: 0x2c3e50,
        antialias: true
      });

      // Add canvas to container
      canvasContainer.appendChild(pixiApp.canvas);

      // Prevent context menu on right-click
      pixiApp.canvas.addEventListener('contextmenu', (e) => {
        e.preventDefault();
      });

      // Enable z-index sorting
      pixiApp.stage.sortableChildren = true;

      // Create containers
      worldContainer = new PIXI.Container();
      worldContainer.sortableChildren = true;
      worldContainer.zIndex = 10;
      pixiApp.stage.addChild(worldContainer);

      // Set up layer containers
      layers.forEach(layer => {
        layer.container.sortableChildren = true;
        layer.container.zIndex = layer.zIndex;
        layer.container.visible = layer.visible;
        worldContainer.addChild(layer.container);
      });

      gridContainer = new PIXI.Container();
      gridContainer.zIndex = 5;
      pixiApp.stage.addChild(gridContainer);

      uiContainer = new PIXI.Container();
      uiContainer.zIndex = 100;
      pixiApp.stage.addChild(uiContainer);

      // Initialize tileset manager
      tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);
      await tilesetManager.loadTileset('/assets/Tileset-Spritesheet.png');

      // Get available tiles
      availableTiles = tilesetManager.getAvailableTileIds();

      // Initialize world builder
      worldBuilder = new WorldBuilder(tilesetManager);
      const tileContainer = worldBuilder.getContainer();
      worldContainer.addChild(tileContainer);

      // Set up interaction
      setupInteraction();

      // Set up keyboard shortcuts
      setupKeyboardShortcuts();

      // Draw initial grid
      drawGrid();

      // Save initial state for undo
      saveTileState();

      toolInitialized = true;
      console.log('Tile positioning tool initialized!');
    } catch (error) {
      console.error('Failed to initialize tile positioning tool:', error);
    }
  }

  function setupInteraction() {
    // Make stage interactive
    pixiApp.stage.eventMode = 'static';
    pixiApp.stage.hitArea = pixiApp.screen;

    // Mouse move for preview and dragging
    pixiApp.stage.on('pointermove', (event) => {
      const globalPos = event.global;
      mouseX = globalPos.x;
      mouseY = globalPos.y;

      // Snap to grid
      snappedX = Math.floor(mouseX / gridSize) * gridSize;
      snappedY = Math.floor(mouseY / gridSize) * gridSize;

      if (isDragging && draggedTileIndex >= 0) {
        // Update dragged tile position - snap to grid during drag
        placedTiles[draggedTileIndex].x = snappedX;
        placedTiles[draggedTileIndex].y = snappedY;
        placedTiles = placedTiles; // Trigger reactivity

        // Update the visual tile position
        rebuildWorld();
      } else {
        // Check for hovered tile
        hoveredTileIndex = findTileAtPosition(snappedX, snappedY);

        updatePreview();

        // Update tile borders to show hover effect
        if (showTileBorders) {
          drawTileBorders();
        }
      }
    });

    // Mouse down - handle both placement and dragging
    pixiApp.stage.on('pointerdown', (event) => {
      const globalPos = event.global;
      const x = Math.floor(globalPos.x / gridSize) * gridSize;
      const y = Math.floor(globalPos.y / gridSize) * gridSize;

      if (event.button === 1) { // Middle mouse button
        // Check if clicking on an existing tile
        const tileIndex = findTileAtPosition(x, y);
        if (tileIndex >= 0) {
          startDragging(tileIndex, globalPos.x, globalPos.y);
        }
      } else if (event.button === 0) { // Left mouse button
        if (!isDragging) {
          const tileId = getSelectedTileId();
          placeTile(tileId, x, y, 2);
        }
      } else if (event.button === 2) { // Right mouse button
        // Delete tile at this position
        const tileIndex = findTileAtPosition(x, y);
        if (tileIndex >= 0) {
          deleteTile(tileIndex);
        }
      }
    });

    // Mouse up - stop dragging
    pixiApp.stage.on('pointerup', (event) => {
      if (event.button === 1 && isDragging) { // Middle mouse button
        stopDragging();
      }
    });

    // Handle mouse leave to stop dragging
    pixiApp.stage.on('pointerleave', () => {
      if (isDragging) {
        stopDragging();
      }
    });
  }

  function drawGrid() {
    gridContainer.removeChildren();

    if (!showGrid) return;

    const gridGraphics = new PIXI.Graphics();

    // Main grid lines (every gridSize pixels)
    gridGraphics.stroke({ width: 1, color: 0x34495e, alpha: 0.3 });

    // Vertical lines
    for (let x = 0; x <= screenWidth; x += gridSize) {
      gridGraphics.moveTo(x, 0);
      gridGraphics.lineTo(x, screenHeight);
    }

    // Horizontal lines
    for (let y = 0; y <= screenHeight; y += gridSize) {
      gridGraphics.moveTo(0, y);
      gridGraphics.lineTo(screenWidth, y);
    }

    // Add thicker lines every 5 grid units for better reference
    gridGraphics.stroke({ width: 2, color: 0x3498db, alpha: 0.2 });

    // Major vertical lines (every 5 grid units)
    for (let x = 0; x <= screenWidth; x += gridSize * 5) {
      gridGraphics.moveTo(x, 0);
      gridGraphics.lineTo(x, screenHeight);
    }

    // Major horizontal lines (every 5 grid units)
    for (let y = 0; y <= screenHeight; y += gridSize * 5) {
      gridGraphics.moveTo(0, y);
      gridGraphics.lineTo(screenWidth, y);
    }

    gridContainer.addChild(gridGraphics);

    // Add world boundary indicator (typical game world size)
    const worldBoundary = new PIXI.Graphics();
    worldBoundary.stroke({ width: 3, color: 0xe74c3c, alpha: 0.6 });

    // Draw a boundary rectangle representing a typical game world (1600x900)
    const worldWidth = Math.min(1600, screenWidth);
    const worldHeight = Math.min(900, screenHeight);
    worldBoundary.rect(0, 0, worldWidth, worldHeight);

    gridContainer.addChild(worldBoundary);
  }

  function updatePreview() {
    // Remove existing preview
    const existingPreview = uiContainer.children.find(child => child.label === 'preview');
    if (existingPreview) {
      uiContainer.removeChild(existingPreview);
    }

    // Don't show preview when dragging
    if (isDragging) {
      return;
    }

    // Create new preview
    const tileId = getSelectedTileId();
    const previewSprite = tilesetManager.createTileSprite(tileId, 2);
    if (previewSprite) {
      previewSprite.label = 'preview';
      previewSprite.x = snappedX;
      previewSprite.y = snappedY;
      previewSprite.alpha = 0.6;
      previewSprite.tint = 0x3498db; // Blue tint for preview
      uiContainer.addChild(previewSprite);
    }
  }

  function placeTile(tileId: number, x: number, y: number, scale: number) {
    const sprite = worldBuilder.addTile(tileId, x, y, scale);
    if (sprite) {
      placedTiles.push({ id: tileId, x, y, scale, layer: activeLayerIndex });
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState(); // Save state for undo
      console.log(`Placed tile ${tileId} at (${x}, ${y}) on layer ${layers[activeLayerIndex].name}`);
    }
  }

  function findTileAtPosition(x: number, y: number): number {
    for (let i = placedTiles.length - 1; i >= 0; i--) {
      const tile = placedTiles[i];
      const tileSize = gridSize; // Assuming all tiles use the current grid size

      if (x >= tile.x && x < tile.x + tileSize &&
          y >= tile.y && y < tile.y + tileSize) {
        return i;
      }
    }
    return -1;
  }

  function startDragging(tileIndex: number, mouseX: number, mouseY: number) {
    isDragging = true;
    draggedTileIndex = tileIndex;
    dragStartX = mouseX;
    dragStartY = mouseY;

    // No offset needed since we'll snap directly to grid position
    dragOffsetX = 0;
    dragOffsetY = 0;

    const tile = placedTiles[tileIndex];
    console.log(`Started dragging tile ${tileIndex} at (${tile.x}, ${tile.y})`);
  }

  function stopDragging() {
    if (isDragging && draggedTileIndex >= 0) {
      const tile = placedTiles[draggedTileIndex];
      console.log(`Stopped dragging tile ${draggedTileIndex} at (${tile.x}, ${tile.y})`);

      // Save state for undo after drag operation
      saveTileState();

      // Final rebuild to remove drag highlighting
      rebuildWorld();
    }

    isDragging = false;
    draggedTileIndex = -1;
    dragStartX = 0;
    dragStartY = 0;
    dragOffsetX = 0;
    dragOffsetY = 0;
  }

  function rebuildWorld() {
    // Clear all layer containers
    layers.forEach(layer => {
      layer.container.removeChildren();
    });

    placedTiles.forEach((tile, index) => {
      // Create sprite using tilesetManager directly
      const sprite = tilesetManager.createTileSprite(tile.id, tile.scale);

      if (sprite) {
        sprite.x = tile.x;
        sprite.y = tile.y;

        // Apply transformations
        if (tile.rotation) {
          sprite.angle = tile.rotation;
        }

        // Apply flipping by adjusting scale
        if (tile.flipX) {
          sprite.scale.x = -Math.abs(sprite.scale.x);
        }
        if (tile.flipY) {
          sprite.scale.y = -Math.abs(sprite.scale.y);
        }

        // Highlight the tile being dragged
        if (isDragging && index === draggedTileIndex) {
          sprite.tint = 0xffff00; // Yellow tint for dragged tile
          sprite.alpha = 0.8;
        }

        // Add to appropriate layer (default to midground if layer not specified)
        const layerIndex = tile.layer !== undefined ? tile.layer : 1;
        if (layerIndex >= 0 && layerIndex < layers.length) {
          layers[layerIndex].container.addChild(sprite);
        }
      }
    });

    // Draw tile borders if enabled
    if (showTileBorders) {
      drawTileBorders();
    }
  }

  function drawTileBorders() {
    // Remove existing tile borders
    const existingBorders = uiContainer.children.filter(child => child.label === 'tile-border');
    existingBorders.forEach(border => uiContainer.removeChild(border));

    // Draw borders around each placed tile
    placedTiles.forEach((tile, index) => {
      const borderGraphics = new PIXI.Graphics();
      borderGraphics.label = 'tile-border';

      // Different border colors for different states
      let borderColor = 0x3498db; // Default blue
      let borderAlpha = 0.3;
      let borderWidth = 1;

      if (isDragging && index === draggedTileIndex) {
        borderColor = 0xf39c12; // Orange for dragged tile
        borderAlpha = 0.9;
        borderWidth = 3;
      } else if (index === hoveredTileIndex && !isDragging) {
        borderColor = 0x2ecc71; // Green for hovered tile
        borderAlpha = 0.7;
        borderWidth = 2;
      }

      borderGraphics.stroke({ width: borderWidth, color: borderColor, alpha: borderAlpha });
      borderGraphics.rect(tile.x, tile.y, gridSize, gridSize);

      uiContainer.addChild(borderGraphics);
    });

    // Add coordinate labels for hovered or dragged tiles
    if (hoveredTileIndex >= 0 || draggedTileIndex >= 0) {
      const targetIndex = isDragging ? draggedTileIndex : hoveredTileIndex;
      const tile = placedTiles[targetIndex];

      if (tile) {
        const coordText = new PIXI.Text({
          text: `(${tile.x}, ${tile.y})`,
          style: {
            fontFamily: 'Arial',
            fontSize: 12,
            fill: 0xFFFFFF,
            stroke: { color: 0x000000, width: 2 }
          }
        });

        coordText.label = 'tile-border';
        coordText.x = tile.x + gridSize + 5;
        coordText.y = tile.y - 5;

        uiContainer.addChild(coordText);
      }
    }
  }

  function getSelectedTileId(): number {
    if (selectedCategory === 'custom') {
      return selectedTileId;
    }

    const category = jungleTileMap[selectedCategory as keyof typeof jungleTileMap];
    if (category && selectedTileType in category) {
      return category[selectedTileType as keyof typeof category];
    }

    return selectedTileId;
  }

  function createJunglePlatform(startX: number, startY: number, width: number, height: number) {
    autoTilePlatform(startX, startY, width, height);
  }

  // Smart tile placement - Auto-platform creation
  function autoTilePlatform(startX: number, startY: number, width: number, height: number) {
    const tileSize = gridSize;

    // Clear any existing tiles in the area first
    clearTilesInArea(startX, startY, width * tileSize, height * tileSize);

    // Place corner tiles
    placeTile(jungleTileMap.platform.topLeft, startX, startY, 2);
    placeTile(jungleTileMap.platform.topRight, startX + (width - 1) * tileSize, startY, 2);
    placeTile(jungleTileMap.platform.bottomLeft, startX, startY + (height - 1) * tileSize, 2);
    placeTile(jungleTileMap.platform.bottomRight, startX + (width - 1) * tileSize, startY + (height - 1) * tileSize, 2);

    // Place top and bottom edge tiles
    for (let col = 1; col < width - 1; col++) {
      const x = startX + col * tileSize;
      placeTile(jungleTileMap.platform.topCenter, x, startY, 2);
      placeTile(jungleTileMap.platform.bottomCenter, x, startY + (height - 1) * tileSize, 2);
    }

    // Place left and right edge tiles
    for (let row = 1; row < height - 1; row++) {
      const y = startY + row * tileSize;
      placeTile(jungleTileMap.platform.leftSide, startX, y, 2);
      placeTile(jungleTileMap.platform.rightSide, startX + (width - 1) * tileSize, y, 2);
    }

    // Fill center with varied foliage for natural look
    for (let row = 1; row < height - 1; row++) {
      for (let col = 1; col < width - 1; col++) {
        const x = startX + col * tileSize;
        const y = startY + row * tileSize;

        // Use random foliage tiles for variety
        const foliageTiles = Object.values(jungleTileMap.foliage);
        const randomTile = foliageTiles[Math.floor(Math.random() * foliageTiles.length)];
        placeTile(randomTile, x, y, 2);
      }
    }

    console.log(`Created ${width}x${height} platform at (${startX}, ${startY})`);
  }

  function clearTilesInArea(startX: number, startY: number, width: number, height: number) {
    const tilesToRemove: number[] = [];

    placedTiles.forEach((tile, index) => {
      if (tile.x >= startX && tile.x < startX + width &&
          tile.y >= startY && tile.y < startY + height) {
        tilesToRemove.push(index);
      }
    });

    // Remove tiles in reverse order to maintain indices
    for (let i = tilesToRemove.length - 1; i >= 0; i--) {
      placedTiles.splice(tilesToRemove[i], 1);
    }

    if (tilesToRemove.length > 0) {
      placedTiles = placedTiles; // Trigger reactivity
      rebuildWorld();
      console.log(`Cleared ${tilesToRemove.length} tiles from area`);
    }
  }

  // Additional smart placement functions
  function createHorizontalPlatform(startX: number, startY: number, length: number) {
    autoTilePlatform(startX, startY, length, 1);
  }

  function createVerticalPlatform(startX: number, startY: number, height: number) {
    autoTilePlatform(startX, startY, 1, height);
  }

  function createStaircase(startX: number, startY: number, steps: number, direction: 'up' | 'down' = 'up') {
    for (let i = 0; i < steps; i++) {
      const x = startX + i * gridSize;
      const y = direction === 'up'
        ? startY - i * gridSize
        : startY + i * gridSize;

      // Create a single tile platform for each step
      placeTile(jungleTileMap.platform.topLeft, x, y, 2);
    }

    console.log(`Created ${steps}-step staircase going ${direction} at (${startX}, ${startY})`);
  }

  function deleteTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const deletedTile = placedTiles[tileIndex];
      placedTiles.splice(tileIndex, 1);
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState(); // Save state for undo
      rebuildWorld();
      console.log(`Deleted tile ${deletedTile.id} at (${deletedTile.x}, ${deletedTile.y})`);
    }
  }

  function clearAllTiles() {
    worldBuilder.clearWorld();
    placedTiles = [];
  }

  function generateCode() {
    let code = '// Generated tile positioning code\n';
    code += '// Copy this into your createBasicWorld() or createFarmScene() function\n\n';
    
    placedTiles.forEach((tile, index) => {
      code += `worldBuilder.addTile(${tile.id}, ${tile.x}, ${tile.y}, ${tile.scale}); // Tile ${index + 1}\n`;
    });

    return code;
  }

  function exportCode() {
    const code = generateCode();
    navigator.clipboard.writeText(code).then(() => {
      alert('Code copied to clipboard!');
    }).catch(() => {
      // Fallback: show in alert
      alert('Copy this code:\n\n' + code);
    });
  }

  // Export/Import functionality
  function exportLayout() {
    const layoutData = {
      tiles: placedTiles,
      gridSize,
      worldSize: { width: screenWidth, height: screenHeight },
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0'
      }
    };

    const dataStr = JSON.stringify(layoutData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportName = `banana-world-layout-${new Date().toISOString().split('T')[0]}.json`;
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportName);
    linkElement.click();

    console.log('Layout exported:', exportName);
  }

  async function importLayout(file: File) {
    try {
      const text = await file.text();
      const layoutData = JSON.parse(text);

      // Validate data structure
      if (layoutData.tiles && Array.isArray(layoutData.tiles)) {
        // Save current state for undo before importing
        saveTileState();

        // Apply imported data
        gridSize = layoutData.gridSize || gridSize;
        placedTiles = layoutData.tiles;

        // Rebuild the world with imported tiles
        rebuildWorld();
        drawGrid();

        console.log('Layout imported successfully:', layoutData.metadata?.exportDate || 'Unknown date');
        alert(`Layout imported successfully! Loaded ${placedTiles.length} tiles.`);
      } else {
        throw new Error('Invalid layout file format');
      }
    } catch (error) {
      console.error('Failed to import layout:', error);
      alert('Failed to import layout. Please check the file format.');
    }
  }

  function handleFileImport(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (file) {
      importLayout(file);
      // Reset input so same file can be imported again
      input.value = '';
    }
  }

  // Undo/Redo functionality
  function saveTileState() {
    const currentState = JSON.stringify(placedTiles);
    undoStack.push(currentState);

    // Limit undo stack size
    if (undoStack.length > maxUndoSteps) {
      undoStack.shift();
    }

    // Clear redo stack on new action
    redoStack = [];
  }

  function undo() {
    if (undoStack.length > 1) { // Keep at least one state (initial)
      const currentState = JSON.stringify(placedTiles);
      redoStack.push(currentState);

      // Remove current state and restore previous
      undoStack.pop();
      const previousState = undoStack[undoStack.length - 1];
      placedTiles = JSON.parse(previousState);
      rebuildWorld();

      console.log('Undo: Restored previous state');
    }
  }

  function redo() {
    if (redoStack.length > 0) {
      const currentState = JSON.stringify(placedTiles);
      undoStack.push(currentState);

      const nextState = redoStack.pop()!;
      placedTiles = JSON.parse(nextState);
      rebuildWorld();

      console.log('Redo: Restored next state');
    }
  }

  // Copy/Paste functionality
  function copyTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      copiedTile = { id: tile.id, scale: tile.scale };
      console.log(`Copied tile ${tile.id}`);
    }
  }

  function pasteTile(x: number, y: number) {
    if (copiedTile) {
      placeTile(copiedTile.id, x, y, copiedTile.scale);
      console.log(`Pasted tile ${copiedTile.id} at (${x}, ${y})`);
    }
  }

  // Tile transformation functions
  function rotateTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      tile.rotation = ((tile.rotation || 0) + 90) % 360;
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState();
      rebuildWorld();
      console.log(`Rotated tile ${tileIndex} to ${tile.rotation}°`);
    }
  }

  function flipTileX(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      tile.flipX = !tile.flipX;
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState();
      rebuildWorld();
      console.log(`Flipped tile ${tileIndex} horizontally: ${tile.flipX}`);
    }
  }

  function flipTileY(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      tile.flipY = !tile.flipY;
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState();
      rebuildWorld();
      console.log(`Flipped tile ${tileIndex} vertically: ${tile.flipY}`);
    }
  }

  // Layer management functions
  function setActiveLayer(index: number) {
    if (index >= 0 && index < layers.length) {
      activeLayerIndex = index;
      console.log(`Active layer set to: ${layers[index].name}`);
    }
  }

  function toggleLayerVisibility(index: number) {
    if (index >= 0 && index < layers.length) {
      layers[index].visible = !layers[index].visible;
      layers[index].container.visible = layers[index].visible;
      layers = layers; // Trigger reactivity
      console.log(`Layer ${layers[index].name} visibility: ${layers[index].visible}`);
    }
  }

  // Keyboard shortcuts
  function setupKeyboardShortcuts() {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!visible) return;

      // Undo: Ctrl+Z
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
      }

      // Redo: Ctrl+Y or Ctrl+Shift+Z
      if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
        e.preventDefault();
        redo();
      }

      // Delete: Del key when hovering over tile
      if (e.key === 'Delete' && hoveredTileIndex >= 0) {
        e.preventDefault();
        deleteTile(hoveredTileIndex);
      }

      // Copy: Ctrl+C to copy selected tile properties
      if (e.ctrlKey && e.key === 'c' && hoveredTileIndex >= 0) {
        e.preventDefault();
        copyTile(hoveredTileIndex);
      }

      // Paste: Ctrl+V to paste tile at cursor position
      if (e.ctrlKey && e.key === 'v') {
        e.preventDefault();
        pasteTile(snappedX, snappedY);
      }

      // Rotate: R key when hovering over tile
      if (e.key === 'r' && hoveredTileIndex >= 0) {
        e.preventDefault();
        rotateTile(hoveredTileIndex);
      }

      // Flip horizontally: H key when hovering over tile
      if (e.key === 'h' && hoveredTileIndex >= 0) {
        e.preventDefault();
        flipTileX(hoveredTileIndex);
      }

      // Flip vertically: V key when hovering over tile
      if (e.key === 'v' && hoveredTileIndex >= 0 && !e.ctrlKey) {
        e.preventDefault();
        flipTileY(hoveredTileIndex);
      }
    };

    // Store reference for cleanup and add listener
    keydownHandler = handleKeyDown;
    window.addEventListener('keydown', keydownHandler);
  }

  function cleanup() {
    // Remove keyboard event listener
    if (keydownHandler) {
      window.removeEventListener('keydown', keydownHandler);
      keydownHandler = null;
    }

    if (pixiApp) {
      pixiApp.destroy(true);
      pixiApp = null as any;
    }
    toolInitialized = false;
  }

  // Reactive updates
  $: if (toolInitialized && showGrid !== undefined) {
    drawGrid();
  }

  $: if (toolInitialized && gridSize) {
    drawGrid();
  }

  $: if (toolInitialized && showTileBorders !== undefined) {
    rebuildWorld();
  }
</script>

{#if visible}
<div class="tile-tool-overlay">
  <!-- Tool Controls -->
  <div class="tool-controls">
    <div class="control-group">
      <label>
        Tile Category:
        <select bind:value={selectedCategory}>
          <option value="platform">Platform Structure</option>
          <option value="foliage">Jungle Foliage</option>
          <option value="custom">Custom Tile ID</option>
        </select>
      </label>
    </div>

    {#if selectedCategory === 'platform'}
      <div class="control-group">
        <label>
          Platform Part:
          <select bind:value={selectedTileType}>
            <option value="topLeft">Top Left Corner</option>
            <option value="topCenter">Top Center</option>
            <option value="topRight">Top Right Corner</option>
            <option value="leftSide">Left Side</option>
            <option value="rightSide">Right Side</option>
            <option value="bottomLeft">Bottom Left Corner</option>
            <option value="bottomCenter">Bottom Center</option>
            <option value="bottomRight">Bottom Right Corner</option>
          </select>
        </label>
      </div>
    {:else if selectedCategory === 'foliage'}
      <div class="control-group">
        <label>
          Foliage Type:
          <select bind:value={selectedTileType}>
            <option value="topLeft">Top Left</option>
            <option value="topCenter">Top Center</option>
            <option value="topRight">Top Right</option>
            <option value="centerLeft">Center Left</option>
            <option value="center">Center</option>
            <option value="centerRight">Center Right</option>
            <option value="bottomLeft">Bottom Left</option>
            <option value="bottomCenter">Bottom Center</option>
            <option value="bottomRight">Bottom Right</option>
          </select>
        </label>
      </div>
    {:else}
      <div class="control-group">
        <label>
          Tile ID:
          <select bind:value={selectedTileId}>
            {#each availableTiles as tileId}
              <option value={tileId}>Tile {tileId}</option>
            {/each}
          </select>
        </label>
      </div>
    {/if}

    <div class="control-group">
      <label>
        Grid Size:
        <input type="range" min="16" max="64" step="16" bind:value={gridSize} />
        <span>{gridSize}px</span>
      </label>
    </div>

    <div class="control-group">
      <label>
        <input type="checkbox" bind:checked={showGrid} />
        Show Grid
      </label>
    </div>

    <div class="control-group">
      <label>
        <input type="checkbox" bind:checked={showTileBorders} />
        Show Tile Borders
      </label>
    </div>

    <div class="control-group layer-controls">
      <span class="control-label">Active Layer:</span>
      {#each layers as layer, index}
        <button
          class="layer-button"
          class:active={activeLayerIndex === index}
          on:click={() => setActiveLayer(index)}
        >
          {layer.name}
        </button>
      {/each}
    </div>

    <div class="control-group layer-visibility">
      <span class="control-label">Layer Visibility:</span>
      {#each layers as layer, index}
        <label class="layer-visibility-item">
          <input
            type="checkbox"
            bind:checked={layer.visible}
            on:change={() => toggleLayerVisibility(index)}
          />
          {layer.name}
        </label>
      {/each}
    </div>

    <div class="control-group">
      <button on:click={undo} disabled={undoStack.length <= 1}>Undo (Ctrl+Z)</button>
      <button on:click={redo} disabled={redoStack.length === 0}>Redo (Ctrl+Y)</button>
      <button on:click={clearAllTiles}>Clear All</button>
      <button on:click={exportCode}>Export Code</button>
    </div>

    <div class="control-group smart-placement">
      <span class="control-label">Smart Placement:</span>
      <button on:click={() => createJunglePlatform(snappedX, snappedY, 4, 3)}>
        4x3 Platform
      </button>
      <button on:click={() => createJunglePlatform(snappedX, snappedY, 6, 2)}>
        6x2 Platform
      </button>
      <button on:click={() => createHorizontalPlatform(snappedX, snappedY, 8)}>
        8-Tile Bridge
      </button>
      <button on:click={() => createVerticalPlatform(snappedX, snappedY, 5)}>
        5-Tile Tower
      </button>
      <button on:click={() => createStaircase(snappedX, snappedY, 5, 'up')}>
        5-Step Stairs ↗
      </button>
      <button on:click={() => createStaircase(snappedX, snappedY, 5, 'down')}>
        5-Step Stairs ↘
      </button>
    </div>

    <div class="control-group">
      <button on:click={exportLayout}>💾 Export Layout</button>
      <label class="file-input-label">
        📁 Import Layout
        <input type="file" accept=".json" on:change={handleFileImport} style="display: none;" />
      </label>
    </div>

    {#if hoveredTileIndex >= 0}
      <div class="control-group tile-transform-controls">
        <span>Transform Hovered Tile:</span>
        <button on:click={() => rotateTile(hoveredTileIndex)}>🔄 Rotate (R)</button>
        <button on:click={() => flipTileX(hoveredTileIndex)}>↔️ Flip H (H)</button>
        <button on:click={() => flipTileY(hoveredTileIndex)}>↕️ Flip V (V)</button>
      </div>
    {/if}
  </div>

  <!-- Canvas Container -->
  <div class="canvas-container" bind:this={canvasContainer}></div>

  <!-- Status Info -->
  <div class="status-info">
    <div>Mouse: ({mouseX}, {mouseY})</div>
    <div>Snapped: ({snappedX}, {snappedY})</div>
    <div>Tiles Placed: {placedTiles.length}</div>
    <div>Selected Tile: {getSelectedTileId()} ({selectedCategory} - {selectedTileType})</div>
    <div>Active Layer: {layers[activeLayerIndex].name}</div>
    <div>Undo: {undoStack.length - 1} | Redo: {redoStack.length}</div>
    {#if hoveredTileIndex >= 0}
      {@const tile = placedTiles[hoveredTileIndex]}
      <div>Hovered: Tile {tile.id} | Rot: {tile.rotation || 0}° | FlipX: {tile.flipX || false} | FlipY: {tile.flipY || false}</div>
    {/if}
    {#if copiedTile}
      <div class="copy-status">📋 Copied: Tile {copiedTile.id}</div>
    {/if}
    {#if isDragging}
      <div class="drag-status">🖱️ Dragging tile {draggedTileIndex + 1}</div>
    {/if}
  </div>

  <!-- Instructions -->
  <div class="instructions-bar">
    <div class="instruction">🖱️ Left Click: Place tile</div>
    <div class="instruction">🖱️ Middle Click + Drag: Move tile</div>
    <div class="instruction">🖱️ Right Click: Delete tile</div>
    <div class="instruction">⌨️ Ctrl+Z: Undo</div>
    <div class="instruction">⌨️ Ctrl+Y: Redo</div>
    <div class="instruction">⌨️ Del: Delete hovered tile</div>
    <div class="instruction">⌨️ Ctrl+C: Copy tile</div>
    <div class="instruction">⌨️ Ctrl+V: Paste tile</div>
    <div class="instruction">⌨️ R: Rotate hovered tile</div>
    <div class="instruction">⌨️ H: Flip hovered tile horizontally</div>
    <div class="instruction">⌨️ V: Flip hovered tile vertically</div>
  </div>
</div>
{/if}

<style>
  .tile-tool-overlay {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #2c3e50;
    z-index: 1000;
    display: flex;
    flex-direction: column;
  }

  .tool-controls {
    background: #34495e;
    padding: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    color: white;
  }

  .control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .control-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .control-group select,
  .control-group input[type="range"] {
    padding: 0.25rem;
    border: 1px solid #7f8c8d;
    border-radius: 4px;
    background: #ecf0f1;
  }

  .control-group button {
    padding: 0.5rem 1rem;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .control-group button:hover {
    background: #2980b9;
  }

  .tile-transform-controls {
    background: #2c3e50;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #34495e;
  }

  .tile-transform-controls span {
    color: #ecf0f1;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .file-input-label {
    padding: 0.5rem 1rem;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: inline-block;
  }

  .file-input-label:hover {
    background: #229954;
  }

  .control-label {
    color: #ecf0f1;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .layer-controls {
    background: #2c3e50;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #34495e;
  }

  .layer-button {
    padding: 0.25rem 0.75rem;
    margin: 0 0.25rem;
    background: #7f8c8d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
  }

  .layer-button:hover {
    background: #95a5a6;
  }

  .layer-button.active {
    background: #3498db;
    font-weight: bold;
  }

  .layer-visibility {
    background: #2c3e50;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #34495e;
  }

  .layer-visibility-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0 0.5rem;
    color: #ecf0f1;
    font-size: 0.8rem;
  }

  .canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  .status-info {
    background: #34495e;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 2rem;
    color: white;
    font-size: 0.8rem;
    font-family: monospace;
    align-items: center;
  }

  .drag-status {
    background: #e74c3c;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
    animation: pulse 1s infinite;
  }

  .copy-status {
    background: #27ae60;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .instructions-bar {
    background: #2c3e50;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 2rem;
    color: #bdc3c7;
    font-size: 0.75rem;
    border-top: 1px solid #34495e;
  }

  .instruction {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
</style>
